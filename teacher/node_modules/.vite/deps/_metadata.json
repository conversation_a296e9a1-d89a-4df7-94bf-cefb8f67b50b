{"hash": "73521fbc", "configHash": "42f54c40", "lockfileHash": "431d3635", "browserHash": "157d3b35", "optimized": {"react": {"src": "../../.pnpm/react@19.1.1/node_modules/react/index.js", "file": "react.js", "fileHash": "fe44f8e5", "needsInterop": true}, "react-dom": {"src": "../../.pnpm/react-dom@19.1.1_react@19.1.1/node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "70948117", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../.pnpm/react@19.1.1/node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "28e5f20f", "needsInterop": true}, "react/jsx-runtime": {"src": "../../.pnpm/react@19.1.1/node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "9ab7c188", "needsInterop": true}, "axios": {"src": "../../.pnpm/axios@1.11.0/node_modules/axios/index.js", "file": "axios.js", "fileHash": "6f01789e", "needsInterop": false}, "clsx": {"src": "../../.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "2b819d3e", "needsInterop": false}, "framer-motion": {"src": "../../.pnpm/framer-motion@12.23.12_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "feee117f", "needsInterop": false}, "lucide-react": {"src": "../../.pnpm/lucide-react@0.540.0_react@19.1.1/node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "a17e2bad", "needsInterop": false}, "react-dom/client": {"src": "../../.pnpm/react-dom@19.1.1_react@19.1.1/node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "7b990039", "needsInterop": true}, "react-markdown": {"src": "../../.pnpm/react-markdown@10.1.0_@types+react@19.1.10_react@19.1.1/node_modules/react-markdown/index.js", "file": "react-markdown.js", "fileHash": "763dceab", "needsInterop": false}, "tailwind-merge": {"src": "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "bf37c293", "needsInterop": false}}, "chunks": {"chunk-BPP2D3V2": {"file": "chunk-BPP2D3V2.js"}, "chunk-KJYSAOIG": {"file": "chunk-KJYSAOIG.js"}, "chunk-575JY5N6": {"file": "chunk-575JY5N6.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}